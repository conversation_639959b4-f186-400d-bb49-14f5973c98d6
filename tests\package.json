{"name": "metiscore-security-tests", "version": "1.0.0", "description": "Comprehensive security and privacy test suite for MenoWellness and Partner Support applications", "main": "run-security-tests.js", "scripts": {"test": "node run-security-tests.js", "test:headless": "HEADLESS=true node run-security-tests.js", "test:visible": "HEADLESS=false node run-security-tests.js", "test:meno": "node -e \"const tester = require('./run-security-tests.js'); const t = new tester(); t.initialize().then(() => t.runMenoWellnessTests()).then(() => t.cleanup())\"", "test:partner": "node -e \"const tester = require('./run-security-tests.js'); const t = new tester(); t.initialize().then(() => t.runPartnerSupportTests()).then(() => t.cleanup())\"", "serve:runner": "python -m http.server 8080 || python3 -m http.server 8080", "install:deps": "npm install", "clean": "rm -rf test-results test-screenshots node_modules"}, "dependencies": {"puppeteer": "^21.0.0"}, "devDependencies": {"@types/node": "^20.0.0"}, "keywords": ["security", "privacy", "testing", "healthcare", "compliance", "GDPR", "HIPAA", "PIPEDA", "encryption", "audit"], "author": "Metiscore Health Security Team", "license": "MIT", "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/metiscore/meno-app"}, "bugs": {"url": "https://github.com/metiscore/meno-app/issues"}, "homepage": "https://github.com/metiscore/meno-app#readme"}