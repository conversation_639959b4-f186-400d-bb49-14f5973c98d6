#!/usr/bin/env node

/**
 * Command-line Security Test Runner
 * Automated testing script for CI/CD pipelines
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

const TEST_CONFIG = {
  MENO_WELLNESS_URL: 'http://localhost:3000',
  PARTNER_SUPPORT_URL: 'http://localhost:3001',
  TEST_TIMEOUT: 30000,
  HEADLESS: process.env.HEADLESS !== 'false',
  SCREENSHOT_DIR: './test-screenshots',
  RESULTS_DIR: './test-results'
};

class AutomatedSecurityTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.results = [];
    this.screenshots = [];
  }

  async initialize() {
    console.log('🚀 Initializing automated security testing...');
    
    // Create directories
    if (!fs.existsSync(TEST_CONFIG.SCREENSHOT_DIR)) {
      fs.mkdirSync(TEST_CONFIG.SCREENSHOT_DIR, { recursive: true });
    }
    if (!fs.existsSync(TEST_CONFIG.RESULTS_DIR)) {
      fs.mkdirSync(TEST_CONFIG.RESULTS_DIR, { recursive: true });
    }

    // Launch browser
    this.browser = await puppeteer.launch({
      headless: TEST_CONFIG.HEADLESS,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    });

    this.page = await this.browser.newPage();
    
    // Set viewport and user agent
    await this.page.setViewport({ width: 1280, height: 720 });
    await this.page.setUserAgent('SecurityTestBot/1.0');

    // Enable console logging
    this.page.on('console', msg => {
      if (msg.type() === 'log' && msg.text().includes('Security')) {
        console.log('🔍 Browser:', msg.text());
      }
    });

    // Handle errors
    this.page.on('pageerror', error => {
      console.error('❌ Page Error:', error.message);
    });

    console.log('✅ Browser initialized successfully');
  }

  async checkApplicationAvailability() {
    console.log('🔍 Checking application availability...');
    
    const apps = [
      { name: 'MenoWellness', url: TEST_CONFIG.MENO_WELLNESS_URL },
      { name: 'Partner Support', url: TEST_CONFIG.PARTNER_SUPPORT_URL }
    ];

    for (const app of apps) {
      try {
        await this.page.goto(app.url, { waitUntil: 'networkidle0', timeout: 10000 });
        const title = await this.page.title();
        console.log(`✅ ${app.name}: Available (${title})`);
        
        // Take screenshot
        await this.takeScreenshot(`${app.name.toLowerCase()}-homepage`);
        
      } catch (error) {
        console.error(`❌ ${app.name}: Not available - ${error.message}`);
        throw new Error(`${app.name} application is not running on ${app.url}`);
      }
    }
  }

  async takeScreenshot(name) {
    const filename = `${name}-${Date.now()}.png`;
    const filepath = path.join(TEST_CONFIG.SCREENSHOT_DIR, filename);
    
    await this.page.screenshot({ 
      path: filepath, 
      fullPage: true 
    });
    
    this.screenshots.push({
      name,
      filename,
      filepath,
      timestamp: new Date().toISOString()
    });
    
    console.log(`📸 Screenshot saved: ${filename}`);
  }

  async injectTestScript() {
    console.log('💉 Injecting security test script...');
    
    // Read the test script
    const testScriptPath = path.join(__dirname, 'security-privacy-tests.js');
    const testScript = fs.readFileSync(testScriptPath, 'utf8');
    
    // Inject the script into the page
    await this.page.evaluate(testScript);
    
    console.log('✅ Test script injected successfully');
  }

  async runMenoWellnessTests() {
    console.log('🌸 Running MenoWellness security tests...');
    
    await this.page.goto(TEST_CONFIG.MENO_WELLNESS_URL + '/profile', { 
      waitUntil: 'networkidle0' 
    });
    
    await this.takeScreenshot('meno-profile-page');
    await this.injectTestScript();
    
    // Run tests
    const results = await this.page.evaluate(async () => {
      const tests = new MenoWellnessSecurityTests();
      await tests.runAllTests();
      return tests.results;
    });
    
    this.results.push({
      application: 'MenoWellness',
      tests: results,
      timestamp: new Date().toISOString()
    });
    
    console.log(`✅ MenoWellness tests completed: ${results.length} tests`);
    return results;
  }

  async runPartnerSupportTests() {
    console.log('🤝 Running Partner Support security tests...');
    
    await this.page.goto(TEST_CONFIG.PARTNER_SUPPORT_URL + '/profile', { 
      waitUntil: 'networkidle0' 
    });
    
    await this.takeScreenshot('partner-profile-page');
    await this.injectTestScript();
    
    // Run tests
    const results = await this.page.evaluate(async () => {
      const tests = new PartnerSupportSecurityTests();
      await tests.runAllTests();
      return tests.results;
    });
    
    this.results.push({
      application: 'PartnerSupport',
      tests: results,
      timestamp: new Date().toISOString()
    });
    
    console.log(`✅ Partner Support tests completed: ${results.length} tests`);
    return results;
  }

  async runCrossApplicationTests() {
    console.log('🔄 Running cross-application security tests...');
    
    await this.injectTestScript();
    
    // Run tests
    const results = await this.page.evaluate(async () => {
      const tests = new CrossApplicationSecurityTests();
      await tests.runAllTests();
      return tests.results;
    });
    
    this.results.push({
      application: 'CrossApplication',
      tests: results,
      timestamp: new Date().toISOString()
    });
    
    console.log(`✅ Cross-application tests completed: ${results.length} tests`);
    return results;
  }

  async generateReport() {
    console.log('📊 Generating comprehensive test report...');
    
    const allTests = this.results.flatMap(r => r.tests);
    const passed = allTests.filter(t => t.status === 'PASSED').length;
    const partial = allTests.filter(t => t.status === 'PARTIAL').length;
    const failed = allTests.filter(t => t.status === 'FAILED').length;
    const total = allTests.length;
    
    const report = {
      summary: {
        timestamp: new Date().toISOString(),
        total,
        passed,
        partial,
        failed,
        successRate: Math.round(((passed + (partial * 0.5)) / total) * 100),
        overallStatus: failed === 0 ? 'PASS' : 'FAIL'
      },
      applications: this.results,
      screenshots: this.screenshots,
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        headless: TEST_CONFIG.HEADLESS,
        urls: {
          menoWellness: TEST_CONFIG.MENO_WELLNESS_URL,
          partnerSupport: TEST_CONFIG.PARTNER_SUPPORT_URL
        }
      },
      criticalSecurityChecklist: this.generateSecurityChecklist(allTests)
    };
    
    // Save report
    const reportPath = path.join(TEST_CONFIG.RESULTS_DIR, `security-report-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    // Generate HTML report
    const htmlReport = this.generateHTMLReport(report);
    const htmlPath = path.join(TEST_CONFIG.RESULTS_DIR, `security-report-${Date.now()}.html`);
    fs.writeFileSync(htmlPath, htmlReport);
    
    console.log(`📄 Report saved: ${reportPath}`);
    console.log(`🌐 HTML Report: ${htmlPath}`);
    
    // Print summary
    this.printSummary(report.summary);
    
    return report;
  }

  generateSecurityChecklist(allTests) {
    const criticalTests = [
      'Consent Management',
      'Encryption Key Management',
      'Data Export Functionality', 
      'Data Deletion Process',
      'Encryption Consistency',
      'Data Isolation'
    ];
    
    return criticalTests.map(testName => {
      const test = allTests.find(t => t.test === testName);
      return {
        test: testName,
        status: test?.status || 'NOT_FOUND',
        critical: true,
        passed: test?.status === 'PASSED'
      };
    });
  }

  generateHTMLReport(report) {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Security Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; }
        .summary { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin: 20px 0; }
        .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .passed { border-left: 4px solid #28a745; }
        .partial { border-left: 4px solid #ffc107; }
        .failed { border-left: 4px solid #dc3545; }
        .total { border-left: 4px solid #6c757d; }
        .test-results { margin: 20px 0; }
        .test-item { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .test-item.PASSED { background: #d4edda; }
        .test-item.PARTIAL { background: #fff3cd; }
        .test-item.FAILED { background: #f8d7da; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔒 Security Test Report</h1>
        <p>Generated: ${report.summary.timestamp}</p>
        <p>Overall Status: <strong>${report.summary.overallStatus}</strong></p>
        <p>Success Rate: <strong>${report.summary.successRate}%</strong></p>
    </div>
    
    <div class="summary">
        <div class="card total">
            <h2>${report.summary.total}</h2>
            <p>Total Tests</p>
        </div>
        <div class="card passed">
            <h2>${report.summary.passed}</h2>
            <p>Passed</p>
        </div>
        <div class="card partial">
            <h2>${report.summary.partial}</h2>
            <p>Partial</p>
        </div>
        <div class="card failed">
            <h2>${report.summary.failed}</h2>
            <p>Failed</p>
        </div>
    </div>
    
    <div class="test-results">
        <h2>Test Results by Application</h2>
        ${report.applications.map(app => `
            <h3>${app.application}</h3>
            ${app.tests.map(test => `
                <div class="test-item ${test.status}">
                    <strong>${test.test}</strong>: ${test.status}
                    ${test.error ? `<br><small>Error: ${test.error}</small>` : ''}
                </div>
            `).join('')}
        `).join('')}
    </div>
    
    <div class="critical-checklist">
        <h2>Critical Security Checklist</h2>
        ${report.criticalSecurityChecklist.map(item => `
            <div class="test-item ${item.status}">
                <strong>${item.test}</strong>: ${item.status}
            </div>
        `).join('')}
    </div>
</body>
</html>`;
  }

  printSummary(summary) {
    console.log('\n' + '='.repeat(60));
    console.log('🎯 AUTOMATED SECURITY TEST SUMMARY');
    console.log('='.repeat(60));
    console.log(`📊 Total Tests: ${summary.total}`);
    console.log(`✅ Passed: ${summary.passed} (${Math.round((summary.passed/summary.total)*100)}%)`);
    console.log(`⚠️  Partial: ${summary.partial} (${Math.round((summary.partial/summary.total)*100)}%)`);
    console.log(`❌ Failed: ${summary.failed} (${Math.round((summary.failed/summary.total)*100)}%)`);
    console.log(`🏆 Success Rate: ${summary.successRate}%`);
    console.log(`🎯 Overall Status: ${summary.overallStatus}`);
    console.log('='.repeat(60));
    
    if (summary.overallStatus === 'FAIL') {
      console.log('❗ CRITICAL: Security tests failed. Review results before deployment.');
      process.exit(1);
    } else {
      console.log('🎉 SUCCESS: All critical security tests passed.');
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('🧹 Browser cleanup completed');
    }
  }

  async runAllTests() {
    try {
      await this.initialize();
      await this.checkApplicationAvailability();
      
      await this.runMenoWellnessTests();
      await this.runPartnerSupportTests();
      await this.runCrossApplicationTests();
      
      const report = await this.generateReport();
      return report;
      
    } catch (error) {
      console.error('❌ Test execution failed:', error);
      throw error;
    } finally {
      await this.cleanup();
    }
  }
}

// CLI execution
if (require.main === module) {
  const tester = new AutomatedSecurityTester();
  
  tester.runAllTests()
    .then(report => {
      console.log('✅ Automated security testing completed successfully');
    })
    .catch(error => {
      console.error('❌ Automated security testing failed:', error);
      process.exit(1);
    });
}

module.exports = AutomatedSecurityTester;
