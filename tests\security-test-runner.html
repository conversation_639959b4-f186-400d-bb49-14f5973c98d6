<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security & Privacy Test Runner</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        
        .test-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
        }
        
        .test-button:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .danger-button {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .danger-button:hover {
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        
        .success-button {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
            box-shadow: 0 4px 15px rgba(81, 207, 102, 0.3);
        }
        
        .success-button:hover {
            box-shadow: 0 6px 20px rgba(81, 207, 102, 0.4);
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 500;
        }
        
        .status.info {
            background: #e3f2fd;
            color: #1565c0;
            border-left: 4px solid #2196f3;
        }
        
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }
        
        .status.warning {
            background: #fff3e0;
            color: #ef6c00;
            border-left: 4px solid #ff9800;
        }
        
        .status.error {
            background: #ffebee;
            color: #c62828;
            border-left: 4px solid #f44336;
        }
        
        .console-output {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
            white-space: pre-wrap;
            border: 2px solid #333;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .test-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .summary-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 2px solid #e9ecef;
        }
        
        .summary-card h3 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: bold;
        }
        
        .summary-card.passed h3 { color: #28a745; }
        .summary-card.partial h3 { color: #ffc107; }
        .summary-card.failed h3 { color: #dc3545; }
        .summary-card.total h3 { color: #6c757d; }
        
        .app-links {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .app-link {
            background: white;
            color: #667eea;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 6px;
            border: 2px solid #667eea;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .app-link:hover {
            background: #667eea;
            color: white;
        }
        
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #667eea;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 Security & Privacy Test Suite</h1>
        
        <div class="instructions">
            <h3>📋 Test Instructions</h3>
            <ol>
                <li><strong>Ensure both applications are running:</strong> MenoWellness (port 3000) and Partner Support (port 3001)</li>
                <li><strong>Sign in to both applications</strong> using Google OAuth before running tests</li>
                <li><strong>Run individual tests</strong> or use "Run All Tests" for comprehensive validation</li>
                <li><strong>Review console output</strong> for detailed test results and security recommendations</li>
                <li><strong>Address any failed tests</strong> before deploying to production</li>
            </ol>
        </div>
        
        <div class="app-links">
            <a href="http://localhost:3000" target="_blank" class="app-link">🌸 MenoWellness App</a>
            <a href="http://localhost:3001" target="_blank" class="app-link">🤝 Partner Support App</a>
        </div>
        
        <div class="status info" id="status">
            Ready to run security tests. Please ensure both applications are running and you're signed in.
        </div>
        
        <div class="progress-bar" id="progressContainer" style="display: none;">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        
        <div class="test-controls">
            <button class="test-button success-button" onclick="runAllTests()">
                🚀 Run All Security Tests
            </button>
            <button class="test-button" onclick="runMenoWellnessTests()">
                🌸 Test MenoWellness
            </button>
            <button class="test-button" onclick="runPartnerSupportTests()">
                🤝 Test Partner Support
            </button>
            <button class="test-button" onclick="runCrossAppTests()">
                🔄 Test Cross-Application
            </button>
            <button class="test-button" onclick="testEncryptionOnly()">
                🔐 Test Encryption Only
            </button>
            <button class="test-button" onclick="testConsentOnly()">
                📋 Test Consent Only
            </button>
            <button class="test-button danger-button" onclick="clearConsole()">
                🗑️ Clear Console
            </button>
            <button class="test-button" onclick="exportResults()">
                📊 Export Results
            </button>
        </div>
        
        <div class="test-summary" id="testSummary" style="display: none;">
            <div class="summary-card total">
                <h3 id="totalTests">0</h3>
                <p>Total Tests</p>
            </div>
            <div class="summary-card passed">
                <h3 id="passedTests">0</h3>
                <p>Passed</p>
            </div>
            <div class="summary-card partial">
                <h3 id="partialTests">0</h3>
                <p>Partial</p>
            </div>
            <div class="summary-card failed">
                <h3 id="failedTests">0</h3>
                <p>Failed</p>
            </div>
        </div>
        
        <div class="console-output" id="consoleOutput">
            Console output will appear here...
            
🔒 Security & Privacy Test Suite Ready
=====================================

Available Test Suites:
• MenoWellness Security Tests (7 tests)
• Partner Support Security Tests (7 tests)  
• Cross-Application Security Tests (5 tests)

Total: 19 comprehensive security validations

Click "Run All Security Tests" to begin comprehensive validation.
        </div>
    </div>

    <script src="security-privacy-tests.js"></script>
    <script>
        let testResults = [];
        let isTestRunning = false;

        // Override console.log to capture output
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            const output = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            appendToConsole(output);
        };

        function appendToConsole(text) {
            const consoleOutput = document.getElementById('consoleOutput');
            consoleOutput.textContent += text + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function updateProgress(percentage) {
            const progressContainer = document.getElementById('progressContainer');
            const progressFill = document.getElementById('progressFill');
            
            if (percentage > 0) {
                progressContainer.style.display = 'block';
                progressFill.style.width = percentage + '%';
            } else {
                progressContainer.style.display = 'none';
            }
        }

        function updateSummary(results) {
            const summary = document.getElementById('testSummary');
            const total = results.length;
            const passed = results.filter(r => r.status === 'PASSED').length;
            const partial = results.filter(r => r.status === 'PARTIAL').length;
            const failed = results.filter(r => r.status === 'FAILED').length;

            document.getElementById('totalTests').textContent = total;
            document.getElementById('passedTests').textContent = passed;
            document.getElementById('partialTests').textContent = partial;
            document.getElementById('failedTests').textContent = failed;

            summary.style.display = 'grid';
        }

        function setButtonsDisabled(disabled) {
            const buttons = document.querySelectorAll('.test-button');
            buttons.forEach(button => {
                if (!button.textContent.includes('Clear Console')) {
                    button.disabled = disabled;
                }
            });
        }

        async function runAllTests() {
            if (isTestRunning) return;
            
            isTestRunning = true;
            setButtonsDisabled(true);
            testResults = [];
            
            updateStatus('Running comprehensive security test suite...', 'info');
            updateProgress(10);
            
            try {
                const runner = new SecurityTestRunner();
                await runner.runAllSecurityTests();
                
                testResults = runner.allResults;
                updateSummary(testResults);
                updateProgress(100);
                
                const successRate = Math.round(
                    (testResults.filter(r => r.status === 'PASSED').length / testResults.length) * 100
                );
                
                if (successRate >= 90) {
                    updateStatus(`🎉 Excellent! Security score: ${successRate}%`, 'success');
                } else if (successRate >= 75) {
                    updateStatus(`👍 Good security score: ${successRate}%`, 'warning');
                } else {
                    updateStatus(`⚠️ Security needs improvement: ${successRate}%`, 'error');
                }
                
            } catch (error) {
                updateStatus('❌ Test execution failed: ' + error.message, 'error');
                console.error('Test execution error:', error);
            } finally {
                isTestRunning = false;
                setButtonsDisabled(false);
                setTimeout(() => updateProgress(0), 2000);
            }
        }

        async function runMenoWellnessTests() {
            if (isTestRunning) return;
            
            isTestRunning = true;
            setButtonsDisabled(true);
            
            updateStatus('Testing MenoWellness security features...', 'info');
            updateProgress(20);
            
            try {
                const tests = new MenoWellnessSecurityTests();
                await tests.runAllTests();
                
                testResults = tests.results;
                updateSummary(testResults);
                updateProgress(100);
                updateStatus('MenoWellness security tests completed', 'success');
                
            } catch (error) {
                updateStatus('❌ MenoWellness tests failed: ' + error.message, 'error');
            } finally {
                isTestRunning = false;
                setButtonsDisabled(false);
                setTimeout(() => updateProgress(0), 2000);
            }
        }

        async function runPartnerSupportTests() {
            if (isTestRunning) return;
            
            isTestRunning = true;
            setButtonsDisabled(true);
            
            updateStatus('Testing Partner Support security features...', 'info');
            updateProgress(20);
            
            try {
                const tests = new PartnerSupportSecurityTests();
                await tests.runAllTests();
                
                testResults = tests.results;
                updateSummary(testResults);
                updateProgress(100);
                updateStatus('Partner Support security tests completed', 'success');
                
            } catch (error) {
                updateStatus('❌ Partner Support tests failed: ' + error.message, 'error');
            } finally {
                isTestRunning = false;
                setButtonsDisabled(false);
                setTimeout(() => updateProgress(0), 2000);
            }
        }

        async function runCrossAppTests() {
            if (isTestRunning) return;
            
            isTestRunning = true;
            setButtonsDisabled(true);
            
            updateStatus('Testing cross-application security...', 'info');
            updateProgress(20);
            
            try {
                const tests = new CrossApplicationSecurityTests();
                await tests.runAllTests();
                
                testResults = tests.results;
                updateSummary(testResults);
                updateProgress(100);
                updateStatus('Cross-application security tests completed', 'success');
                
            } catch (error) {
                updateStatus('❌ Cross-application tests failed: ' + error.message, 'error');
            } finally {
                isTestRunning = false;
                setButtonsDisabled(false);
                setTimeout(() => updateProgress(0), 2000);
            }
        }

        async function testEncryptionOnly() {
            updateStatus('Testing encryption features only...', 'info');
            console.log('🔐 Running Encryption-Only Tests...');
            
            const encryptionStatus = await SecurityTestUtils.checkEncryptionStatus();
            console.log('Encryption Status:', encryptionStatus);
            
            updateStatus('Encryption test completed - check console for details', 'success');
        }

        async function testConsentOnly() {
            updateStatus('Testing consent management only...', 'info');
            console.log('📋 Running Consent-Only Tests...');
            
            // Simple consent validation
            const consentElements = document.querySelectorAll('*:contains("consent")');
            console.log(`Found ${consentElements.length} consent-related elements`);
            
            updateStatus('Consent test completed - check console for details', 'success');
        }

        function clearConsole() {
            document.getElementById('consoleOutput').textContent = 'Console cleared.\n';
            document.getElementById('testSummary').style.display = 'none';
            updateStatus('Console cleared. Ready for new tests.', 'info');
        }

        function exportResults() {
            if (testResults.length === 0) {
                updateStatus('No test results to export. Run tests first.', 'warning');
                return;
            }
            
            const exportData = {
                timestamp: new Date().toISOString(),
                testResults: testResults,
                summary: {
                    total: testResults.length,
                    passed: testResults.filter(r => r.status === 'PASSED').length,
                    partial: testResults.filter(r => r.status === 'PARTIAL').length,
                    failed: testResults.filter(r => r.status === 'FAILED').length
                }
            };
            
            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `security-test-results-${Date.now()}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            updateStatus('Test results exported successfully', 'success');
        }

        // Check if apps are running on page load
        window.addEventListener('load', async () => {
            try {
                const menoResponse = await fetch('http://localhost:3000', { mode: 'no-cors' });
                const partnerResponse = await fetch('http://localhost:3001', { mode: 'no-cors' });
                updateStatus('✅ Both applications detected. Ready to run tests.', 'success');
            } catch (error) {
                updateStatus('⚠️ Please ensure both applications are running before testing.', 'warning');
            }
        });
    </script>
</body>
</html>
