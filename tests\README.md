# 🔒 Security & Privacy Test Suite

Comprehensive security and privacy testing framework for the MenoWellness and Partner Support applications. This test suite validates all data privacy features, security implementations, and compliance requirements across both applications.

## 🎯 Overview

This test suite provides comprehensive validation of:

- **Data Privacy & Consent Management** - GDPR/HIPAA/PIPEDA compliance
- **End-to-End Encryption** - Client-side encryption validation
- **Data Export & Deletion** - Right to be forgotten implementation
- **Access Controls** - User data isolation and permissions
- **Audit Logging** - Security event tracking
- **Cross-Application Security** - Shared component validation
- **Partner Data Sharing** - Secure relationship management

## 📋 Test Coverage

### MenoWellness App Tests (7 tests)
- ✅ Profile Page Access
- ✅ Consent Management
- ✅ Encryption Key Management
- ✅ Data Export Functionality
- ✅ Data Deletion Process
- ✅ Jurisdiction Detection
- ✅ Audit Logging

### Partner Support App Tests (7 tests)
- ✅ Partner Profile Page Access
- ✅ Partner-Specific Consent
- ✅ Data Access Controls
- ✅ Partner Data Export
- ✅ Partner Data Deletion
- ✅ Shared Data Security
- ✅ Partner Invite System

### Cross-Application Tests (5 tests)
- ✅ Encryption Consistency
- ✅ Data Isolation
- ✅ Shared Component Security
- ✅ Backend Security Integration
- ✅ Compliance Consistency

**Total: 19 comprehensive security validations**

## 🚀 Quick Start

### Prerequisites

1. **Applications Running**:
   ```bash
   # In the main project directory
   pnpm dev:frontend
   ```
   - MenoWellness: http://localhost:3000
   - Partner Support: http://localhost:3001

2. **User Authentication**: Sign in to both applications using Google OAuth before running tests

### Browser-Based Testing (Recommended)

1. **Open the test runner**:
   ```bash
   cd tests
   python -m http.server 8080
   # Or: python3 -m http.server 8080
   ```

2. **Navigate to**: http://localhost:8080/security-test-runner.html

3. **Run tests** using the interactive interface

### Automated Testing (CI/CD)

1. **Install dependencies**:
   ```bash
   cd tests
   npm install
   ```

2. **Run all tests**:
   ```bash
   npm test
   ```

3. **Run specific test suites**:
   ```bash
   npm run test:meno      # MenoWellness only
   npm run test:partner   # Partner Support only
   npm run test:visible   # Run with visible browser
   ```

## 📊 Test Results

### Browser Console Output
Tests provide real-time console output with:
- ✅ Passed tests
- ⚠️ Partial implementations
- ❌ Failed validations
- 📋 Detailed error information

### Automated Reports
Automated tests generate:
- **JSON Report**: `test-results/security-report-{timestamp}.json`
- **HTML Report**: `test-results/security-report-{timestamp}.html`
- **Screenshots**: `test-screenshots/` directory

### Success Criteria
- **90%+ Success Rate**: Excellent security implementation
- **75%+ Success Rate**: Good security with room for improvement
- **<75% Success Rate**: Requires immediate attention

## 🔧 Test Configuration

### Environment Variables
```bash
HEADLESS=true          # Run browser in headless mode (default)
HEADLESS=false         # Run with visible browser for debugging
```

### Test URLs
```javascript
MENO_WELLNESS_URL: 'http://localhost:3000'
PARTNER_SUPPORT_URL: 'http://localhost:3001'
TEST_TIMEOUT: 30000    # 30 seconds
```

## 🛡️ Security Features Tested

### 1. Consent Management
- ✅ Granular consent controls
- ✅ Jurisdiction-specific compliance
- ✅ Consent withdrawal functionality
- ✅ Audit trail for consent changes

### 2. Encryption & Key Management
- ✅ AES-256-GCM client-side encryption
- ✅ Secure key storage in IndexedDB
- ✅ Key rotation capabilities
- ✅ Key backup and restore

### 3. Data Rights (GDPR/HIPAA/PIPEDA)
- ✅ Complete data export
- ✅ Account deletion process
- ✅ Data retention policies
- ✅ Right to be forgotten

### 4. Access Controls
- ✅ User data isolation
- ✅ Partner permission validation
- ✅ Shared data security
- ✅ Role-based access

### 5. Audit & Compliance
- ✅ Security event logging
- ✅ Multi-jurisdictional support
- ✅ Compliance documentation
- ✅ Privacy by design

## 🔍 Manual Testing Checklist

### Before Running Tests
- [ ] Both applications are running and accessible
- [ ] User is signed in to both applications
- [ ] Browser has JavaScript enabled
- [ ] Network connectivity is stable

### During Testing
- [ ] Monitor console output for errors
- [ ] Check for visual indicators of security features
- [ ] Verify consent toggles are functional
- [ ] Test data export/deletion workflows

### After Testing
- [ ] Review test results and success rate
- [ ] Address any failed security tests
- [ ] Document any partial implementations
- [ ] Plan remediation for critical failures

## 🚨 Critical Security Checklist

The following tests are considered **critical** for production deployment:

1. **✅ Consent Management** - Legal compliance requirement
2. **✅ Encryption Key Management** - Data protection requirement
3. **✅ Data Export Functionality** - GDPR Article 20 requirement
4. **✅ Data Deletion Process** - GDPR Article 17 requirement
5. **✅ Encryption Consistency** - Cross-app security requirement
6. **✅ Data Isolation** - Multi-tenant security requirement

**⚠️ All critical tests must pass before production deployment.**

## 🐛 Troubleshooting

### Common Issues

**Applications not accessible**:
```bash
# Check if apps are running
curl http://localhost:3000
curl http://localhost:3001

# Restart if needed
pnpm dev:frontend
```

**Authentication required**:
- Sign in to both applications before running tests
- Ensure Google OAuth is configured correctly

**Test failures**:
- Check browser console for detailed error messages
- Verify all required components are loaded
- Ensure proper network connectivity

**Puppeteer issues** (automated tests):
```bash
# Install dependencies
npm install

# Run with visible browser for debugging
npm run test:visible
```

## 📈 Continuous Integration

### GitHub Actions Example
```yaml
name: Security Tests
on: [push, pull_request]
jobs:
  security-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: pnpm install
      - run: pnpm dev:frontend &
      - run: sleep 30  # Wait for apps to start
      - run: cd tests && npm install && npm test
```

## 📞 Support

For issues with the security test suite:

1. **Check the troubleshooting section** above
2. **Review console output** for specific error messages
3. **Verify prerequisites** are met
4. **Create an issue** with detailed error information

## 🔄 Updates

This test suite should be updated when:
- New security features are added
- Compliance requirements change
- New vulnerabilities are discovered
- Applications undergo major updates

---

**🔒 Security is not a feature, it's a foundation. Test early, test often.**
