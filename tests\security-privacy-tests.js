/**
 * Comprehensive Security and Privacy Test Suite
 * Tests all data privacy and security features across both applications
 */

// Test configuration
const TEST_CONFIG = {
  MENO_WELLNESS_URL: 'http://localhost:3000',
  PARTNER_SUPPORT_URL: 'http://localhost:3001',
  TEST_TIMEOUT: 30000,
  ENCRYPTION_KEY_SIZE: 256,
  REQUIRED_CONSENT_FIELDS: ['dataProcessing', 'sentimentAnalysis', 'anonymizedLicensing', 'researchParticipation']
};

// Test utilities
class SecurityTestUtils {
  static async waitForElement(selector, timeout = 5000) {
    const start = Date.now();
    while (Date.now() - start < timeout) {
      const element = document.querySelector(selector);
      if (element) return element;
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    throw new Error(`Element ${selector} not found within ${timeout}ms`);
  }

  static async simulateUserInteraction(element, action = 'click') {
    if (action === 'click') {
      element.click();
    } else if (action === 'input') {
      element.focus();
      element.dispatchEvent(new Event('input', { bubbles: true }));
    }
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  static generateTestData() {
    return {
      journalEntry: "Test journal entry for encryption validation - " + Date.now(),
      sensitiveData: "Personal health information that should be encrypted",
      testEmail: `test.user.${Date.now()}@example.com`,
      testName: "Test User " + Date.now()
    };
  }

  static async checkEncryptionStatus() {
    // Check if IndexedDB contains encrypted keys
    return new Promise((resolve) => {
      const request = indexedDB.open('MenoEncryptionStore', 2);
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(['keys'], 'readonly');
        const store = transaction.objectStore('keys');
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          const keys = getAllRequest.result;
          resolve({
            hasKeys: keys.length > 0,
            keyCount: keys.length,
            algorithms: keys.map(k => k.algorithm),
            keyIds: keys.map(k => k.userId)
          });
        };
      };
      request.onerror = () => resolve({ hasKeys: false, error: request.error });
    });
  }
}

// Test Suite 1: MenoWellness Profile Security Tests
class MenoWellnessSecurityTests {
  constructor() {
    this.results = [];
    this.testData = SecurityTestUtils.generateTestData();
  }

  async runAllTests() {
    console.log('🌸 Starting MenoWellness Security Tests...');
    
    try {
      await this.testProfilePageAccess();
      await this.testConsentManagement();
      await this.testEncryptionKeyManagement();
      await this.testDataExportFunctionality();
      await this.testDataDeletionProcess();
      await this.testJurisdictionDetection();
      await this.testAuditLogging();
      
      this.generateReport('MenoWellness');
    } catch (error) {
      console.error('❌ MenoWellness test suite failed:', error);
      this.results.push({ test: 'Suite Execution', status: 'FAILED', error: error.message });
    }
  }

  async testProfilePageAccess() {
    try {
      // Navigate to profile page
      window.location.href = TEST_CONFIG.MENO_WELLNESS_URL + '/profile';
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Check if profile page loads
      const profileHeader = await SecurityTestUtils.waitForElement('h1');
      const hasPrivacySection = document.querySelector('[data-testid="privacy-section"]') || 
                               document.querySelector('h2:contains("Privacy")') ||
                               document.querySelector('*:contains("Consent")');

      this.results.push({
        test: 'Profile Page Access',
        status: profileHeader && hasPrivacySection ? 'PASSED' : 'FAILED',
        details: {
          headerFound: !!profileHeader,
          privacySectionFound: !!hasPrivacySection,
          url: window.location.href
        }
      });
    } catch (error) {
      this.results.push({
        test: 'Profile Page Access',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async testConsentManagement() {
    try {
      // Test each consent toggle
      const consentTests = [];
      
      for (const field of TEST_CONFIG.REQUIRED_CONSENT_FIELDS) {
        try {
          const toggle = document.querySelector(`input[name="${field}"]`);
          if (toggle) {
            const initialState = toggle.checked;
            await SecurityTestUtils.simulateUserInteraction(toggle);
            const newState = toggle.checked;
            
            consentTests.push({
              field,
              toggleFound: true,
              stateChanged: initialState !== newState,
              initialState,
              newState
            });
          } else {
            consentTests.push({
              field,
              toggleFound: false,
              error: 'Toggle not found'
            });
          }
        } catch (error) {
          consentTests.push({
            field,
            error: error.message
          });
        }
      }

      // Test save functionality
      const saveButton = document.querySelector('button:contains("Save")') || 
                        document.querySelector('[data-testid="save-consent"]');
      let saveTest = { found: false };
      
      if (saveButton) {
        await SecurityTestUtils.simulateUserInteraction(saveButton);
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const successMessage = document.querySelector('.success') || 
                              document.querySelector('*:contains("saved")') ||
                              document.querySelector('*:contains("✅")');
        
        saveTest = {
          found: true,
          clicked: true,
          successMessageShown: !!successMessage
        };
      }

      this.results.push({
        test: 'Consent Management',
        status: consentTests.every(t => t.toggleFound) && saveTest.found ? 'PASSED' : 'PARTIAL',
        details: {
          consentFields: consentTests,
          saveButton: saveTest,
          totalFields: TEST_CONFIG.REQUIRED_CONSENT_FIELDS.length,
          workingFields: consentTests.filter(t => t.toggleFound).length
        }
      });
    } catch (error) {
      this.results.push({
        test: 'Consent Management',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async testEncryptionKeyManagement() {
    try {
      // Check for encryption key management section
      const keySection = document.querySelector('*:contains("Encryption")') ||
                         document.querySelector('*:contains("Key Management")') ||
                         document.querySelector('[data-testid="key-management"]');

      // Test key rotation if available
      const rotateButton = document.querySelector('button:contains("Rotate")') ||
                          document.querySelector('[data-testid="rotate-key"]');
      
      let rotationTest = { available: false };
      if (rotateButton) {
        await SecurityTestUtils.simulateUserInteraction(rotateButton);
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        rotationTest = {
          available: true,
          clicked: true,
          // Check for success indication
          success: !!(document.querySelector('*:contains("rotated")') || 
                     document.querySelector('*:contains("success")'))
        };
      }

      // Check IndexedDB for encryption keys
      const encryptionStatus = await SecurityTestUtils.checkEncryptionStatus();

      this.results.push({
        test: 'Encryption Key Management',
        status: keySection && encryptionStatus.hasKeys ? 'PASSED' : 'PARTIAL',
        details: {
          keySectionFound: !!keySection,
          rotationTest,
          encryptionStatus,
          indexedDBAvailable: typeof indexedDB !== 'undefined'
        }
      });
    } catch (error) {
      this.results.push({
        test: 'Encryption Key Management',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async testDataExportFunctionality() {
    try {
      // Look for data export button
      const exportButton = document.querySelector('button:contains("Export")') ||
                          document.querySelector('[data-testid="export-data"]') ||
                          document.querySelector('*:contains("Download")');

      let exportTest = { available: false };
      
      if (exportButton) {
        await SecurityTestUtils.simulateUserInteraction(exportButton);
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Check for export progress or completion
        const progressIndicator = document.querySelector('*:contains("progress")') ||
                                 document.querySelector('*:contains("exporting")') ||
                                 document.querySelector('.progress');
        
        const completionIndicator = document.querySelector('*:contains("complete")') ||
                                   document.querySelector('*:contains("download")') ||
                                   document.querySelector('a[download]');

        exportTest = {
          available: true,
          clicked: true,
          progressShown: !!progressIndicator,
          completionShown: !!completionIndicator
        };
      }

      this.results.push({
        test: 'Data Export Functionality',
        status: exportTest.available ? 'PASSED' : 'FAILED',
        details: exportTest
      });
    } catch (error) {
      this.results.push({
        test: 'Data Export Functionality',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async testDataDeletionProcess() {
    try {
      // Look for data deletion option
      const deleteButton = document.querySelector('button:contains("Delete")') ||
                          document.querySelector('[data-testid="delete-account"]') ||
                          document.querySelector('*:contains("Remove")');

      let deletionTest = { available: false };
      
      if (deleteButton) {
        await SecurityTestUtils.simulateUserInteraction(deleteButton);
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Check for confirmation dialog
        const confirmDialog = document.querySelector('.modal') ||
                             document.querySelector('*:contains("confirm")') ||
                             document.querySelector('*:contains("permanent")');
        
        deletionTest = {
          available: true,
          clicked: true,
          confirmationShown: !!confirmDialog,
          // Don't actually confirm deletion in tests
          actuallyDeleted: false
        };
      }

      this.results.push({
        test: 'Data Deletion Process',
        status: deletionTest.available && deletionTest.confirmationShown ? 'PASSED' : 'PARTIAL',
        details: deletionTest
      });
    } catch (error) {
      this.results.push({
        test: 'Data Deletion Process',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async testJurisdictionDetection() {
    try {
      // Check if jurisdiction is displayed
      const jurisdictionText = document.querySelector('*:contains("GDPR")') ||
                              document.querySelector('*:contains("HIPAA")') ||
                              document.querySelector('*:contains("PIPEDA")') ||
                              document.querySelector('*:contains("jurisdiction")');

      // Test timezone-based detection
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const language = navigator.language;

      this.results.push({
        test: 'Jurisdiction Detection',
        status: jurisdictionText ? 'PASSED' : 'PARTIAL',
        details: {
          jurisdictionTextFound: !!jurisdictionText,
          detectedTimezone: timezone,
          detectedLanguage: language,
          browserInfo: {
            userAgent: navigator.userAgent,
            platform: navigator.platform
          }
        }
      });
    } catch (error) {
      this.results.push({
        test: 'Jurisdiction Detection',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async testAuditLogging() {
    try {
      // Test if actions are being logged (check console or network)
      const originalConsoleLog = console.log;
      const loggedActions = [];
      
      console.log = (...args) => {
        if (args.some(arg => typeof arg === 'string' && 
            (arg.includes('audit') || arg.includes('log') || arg.includes('action')))) {
          loggedActions.push(args);
        }
        originalConsoleLog.apply(console, args);
      };

      // Perform some actions that should be logged
      const testButton = document.querySelector('button') || document.querySelector('input');
      if (testButton) {
        await SecurityTestUtils.simulateUserInteraction(testButton);
      }

      // Restore console.log
      console.log = originalConsoleLog;

      this.results.push({
        test: 'Audit Logging',
        status: loggedActions.length > 0 ? 'PASSED' : 'PARTIAL',
        details: {
          actionsLogged: loggedActions.length,
          sampleLogs: loggedActions.slice(0, 3),
          testPerformed: !!testButton
        }
      });
    } catch (error) {
      this.results.push({
        test: 'Audit Logging',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  generateReport(appName) {
    const passed = this.results.filter(r => r.status === 'PASSED').length;
    const partial = this.results.filter(r => r.status === 'PARTIAL').length;
    const failed = this.results.filter(r => r.status === 'FAILED').length;
    const total = this.results.length;

    console.log(`\n📊 ${appName} Security Test Report`);
    console.log(`${'='.repeat(50)}`);
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`⚠️  Partial: ${partial}/${total}`);
    console.log(`❌ Failed: ${failed}/${total}`);
    console.log(`📈 Success Rate: ${Math.round((passed / total) * 100)}%`);

    console.log('\n📋 Detailed Results:');
    this.results.forEach(result => {
      const icon = result.status === 'PASSED' ? '✅' :
                   result.status === 'PARTIAL' ? '⚠️' : '❌';
      console.log(`${icon} ${result.test}: ${result.status}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
      if (result.details) {
        console.log(`   Details:`, result.details);
      }
    });
  }
}

// Test Suite 2: Partner Support Security Tests
class PartnerSupportSecurityTests {
  constructor() {
    this.results = [];
    this.testData = SecurityTestUtils.generateTestData();
  }

  async runAllTests() {
    console.log('🤝 Starting Partner Support Security Tests...');

    try {
      await this.testProfilePageAccess();
      await this.testPartnerSpecificConsent();
      await this.testDataAccessControls();
      await this.testPartnerDataExport();
      await this.testPartnerDataDeletion();
      await this.testSharedDataSecurity();
      await this.testPartnerInviteSystem();

      this.generateReport('Partner Support');
    } catch (error) {
      console.error('❌ Partner Support test suite failed:', error);
      this.results.push({ test: 'Suite Execution', status: 'FAILED', error: error.message });
    }
  }

  async testProfilePageAccess() {
    try {
      // Navigate to partner profile page
      window.location.href = TEST_CONFIG.PARTNER_SUPPORT_URL + '/profile';
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Check if profile page loads with partner-specific content
      const profileHeader = await SecurityTestUtils.waitForElement('h1');
      const partnerContent = document.querySelector('*:contains("Partner")') ||
                            document.querySelector('*:contains("Support")');

      const privacySection = document.querySelector('*:contains("Privacy")') ||
                            document.querySelector('*:contains("Consent")');

      this.results.push({
        test: 'Partner Profile Page Access',
        status: profileHeader && partnerContent && privacySection ? 'PASSED' : 'FAILED',
        details: {
          headerFound: !!profileHeader,
          partnerContentFound: !!partnerContent,
          privacySectionFound: !!privacySection,
          url: window.location.href
        }
      });
    } catch (error) {
      this.results.push({
        test: 'Partner Profile Page Access',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async testPartnerSpecificConsent() {
    try {
      // Test partner-specific consent options
      const partnerConsentFields = [
        'dataProcessing',
        'sentimentAnalysis',
        'anonymizedLicensing',
        'researchParticipation'
      ];

      const consentTests = [];

      for (const field of partnerConsentFields) {
        try {
          const toggle = document.querySelector(`input[name="${field}"]`);
          const label = document.querySelector(`label[for="${field}"]`) ||
                       toggle?.closest('div')?.querySelector('h3, p, span');

          if (toggle && label) {
            const labelText = label.textContent || label.innerText;
            const isPartnerSpecific = labelText.toLowerCase().includes('partner') ||
                                     labelText.toLowerCase().includes('support') ||
                                     labelText.toLowerCase().includes('shared');

            consentTests.push({
              field,
              toggleFound: true,
              labelFound: true,
              isPartnerSpecific,
              labelText: labelText.substring(0, 100)
            });
          } else {
            consentTests.push({
              field,
              toggleFound: !!toggle,
              labelFound: !!label,
              error: 'Toggle or label not found'
            });
          }
        } catch (error) {
          consentTests.push({
            field,
            error: error.message
          });
        }
      }

      this.results.push({
        test: 'Partner-Specific Consent',
        status: consentTests.every(t => t.toggleFound && t.labelFound) ? 'PASSED' : 'PARTIAL',
        details: {
          consentFields: consentTests,
          partnerSpecificFields: consentTests.filter(t => t.isPartnerSpecific).length,
          totalFields: partnerConsentFields.length
        }
      });
    } catch (error) {
      this.results.push({
        test: 'Partner-Specific Consent',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async testDataAccessControls() {
    try {
      // Test that partner can only access appropriate data
      const accessControlTests = [];

      // Check for shared data indicators
      const sharedDataSection = document.querySelector('*:contains("shared")') ||
                               document.querySelector('*:contains("access")');

      // Check for restrictions/limitations mentioned
      const restrictionText = document.querySelector('*:contains("only")') ||
                             document.querySelector('*:contains("limited")') ||
                             document.querySelector('*:contains("permission")');

      accessControlTests.push({
        test: 'Shared Data Section',
        found: !!sharedDataSection,
        content: sharedDataSection?.textContent?.substring(0, 200)
      });

      accessControlTests.push({
        test: 'Access Restrictions',
        found: !!restrictionText,
        content: restrictionText?.textContent?.substring(0, 200)
      });

      this.results.push({
        test: 'Data Access Controls',
        status: accessControlTests.some(t => t.found) ? 'PASSED' : 'PARTIAL',
        details: accessControlTests
      });
    } catch (error) {
      this.results.push({
        test: 'Data Access Controls',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async testPartnerDataExport() {
    try {
      // Test partner-specific data export
      const exportButton = document.querySelector('button:contains("Export")') ||
                          document.querySelector('[data-testid="export-data"]');

      let exportTest = { available: false };

      if (exportButton) {
        // Check if export text mentions partner-specific data
        const exportSection = exportButton.closest('div, section');
        const exportText = exportSection?.textContent || '';
        const isPartnerSpecific = exportText.toLowerCase().includes('partner') ||
                                 exportText.toLowerCase().includes('support') ||
                                 exportText.toLowerCase().includes('interaction');

        await SecurityTestUtils.simulateUserInteraction(exportButton);
        await new Promise(resolve => setTimeout(resolve, 2000));

        exportTest = {
          available: true,
          isPartnerSpecific,
          clicked: true,
          exportText: exportText.substring(0, 300)
        };
      }

      this.results.push({
        test: 'Partner Data Export',
        status: exportTest.available ? 'PASSED' : 'FAILED',
        details: exportTest
      });
    } catch (error) {
      this.results.push({
        test: 'Partner Data Export',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async testPartnerDataDeletion() {
    try {
      // Test partner-specific data deletion
      const deleteButton = document.querySelector('button:contains("Delete")') ||
                          document.querySelector('[data-testid="delete-account"]');

      let deletionTest = { available: false };

      if (deleteButton) {
        const deleteSection = deleteButton.closest('div, section');
        const deleteText = deleteSection?.textContent || '';
        const mentionsPartnerData = deleteText.toLowerCase().includes('partner') ||
                                   deleteText.toLowerCase().includes('interaction') ||
                                   deleteText.toLowerCase().includes('support');

        await SecurityTestUtils.simulateUserInteraction(deleteButton);
        await new Promise(resolve => setTimeout(resolve, 1000));

        const confirmDialog = document.querySelector('.modal') ||
                             document.querySelector('*:contains("confirm")');

        deletionTest = {
          available: true,
          mentionsPartnerData,
          confirmationShown: !!confirmDialog,
          deleteText: deleteText.substring(0, 300)
        };
      }

      this.results.push({
        test: 'Partner Data Deletion',
        status: deletionTest.available ? 'PASSED' : 'FAILED',
        details: deletionTest
      });
    } catch (error) {
      this.results.push({
        test: 'Partner Data Deletion',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async testSharedDataSecurity() {
    try {
      // Test shared data security indicators
      const securityIndicators = [];

      // Look for encryption mentions
      const encryptionText = document.querySelector('*:contains("encrypt")') ||
                            document.querySelector('*:contains("secure")');

      // Look for privacy mentions
      const privacyText = document.querySelector('*:contains("private")') ||
                         document.querySelector('*:contains("confidential")');

      // Look for consent requirements
      const consentText = document.querySelector('*:contains("consent")') ||
                         document.querySelector('*:contains("permission")');

      securityIndicators.push({
        type: 'Encryption',
        found: !!encryptionText,
        text: encryptionText?.textContent?.substring(0, 100)
      });

      securityIndicators.push({
        type: 'Privacy',
        found: !!privacyText,
        text: privacyText?.textContent?.substring(0, 100)
      });

      securityIndicators.push({
        type: 'Consent',
        found: !!consentText,
        text: consentText?.textContent?.substring(0, 100)
      });

      this.results.push({
        test: 'Shared Data Security',
        status: securityIndicators.filter(s => s.found).length >= 2 ? 'PASSED' : 'PARTIAL',
        details: {
          indicators: securityIndicators,
          foundCount: securityIndicators.filter(s => s.found).length
        }
      });
    } catch (error) {
      this.results.push({
        test: 'Shared Data Security',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async testPartnerInviteSystem() {
    try {
      // Test partner invite/connection system
      const inviteElements = [];

      // Look for invite code input
      const inviteInput = document.querySelector('input[placeholder*="invite"]') ||
                         document.querySelector('input[placeholder*="code"]') ||
                         document.querySelector('[data-testid="invite-code"]');

      // Look for connection status
      const connectionStatus = document.querySelector('*:contains("connected")') ||
                              document.querySelector('*:contains("partner")') ||
                              document.querySelector('*:contains("relationship")');

      // Look for invite/connect button
      const connectButton = document.querySelector('button:contains("Connect")') ||
                           document.querySelector('button:contains("Join")') ||
                           document.querySelector('[data-testid="connect-partner"]');

      inviteElements.push({
        element: 'Invite Input',
        found: !!inviteInput,
        type: inviteInput?.type,
        placeholder: inviteInput?.placeholder
      });

      inviteElements.push({
        element: 'Connection Status',
        found: !!connectionStatus,
        text: connectionStatus?.textContent?.substring(0, 100)
      });

      inviteElements.push({
        element: 'Connect Button',
        found: !!connectButton,
        text: connectButton?.textContent
      });

      this.results.push({
        test: 'Partner Invite System',
        status: inviteElements.filter(e => e.found).length >= 1 ? 'PASSED' : 'FAILED',
        details: {
          elements: inviteElements,
          foundCount: inviteElements.filter(e => e.found).length
        }
      });
    } catch (error) {
      this.results.push({
        test: 'Partner Invite System',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  generateReport(appName) {
    const passed = this.results.filter(r => r.status === 'PASSED').length;
    const partial = this.results.filter(r => r.status === 'PARTIAL').length;
    const failed = this.results.filter(r => r.status === 'FAILED').length;
    const total = this.results.length;

    console.log(`\n📊 ${appName} Security Test Report`);
    console.log(`${'='.repeat(50)}`);
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`⚠️  Partial: ${partial}/${total}`);
    console.log(`❌ Failed: ${failed}/${total}`);
    console.log(`📈 Success Rate: ${Math.round((passed / total) * 100)}%`);

    console.log('\n📋 Detailed Results:');
    this.results.forEach(result => {
      const icon = result.status === 'PASSED' ? '✅' :
                   result.status === 'PARTIAL' ? '⚠️' : '❌';
      console.log(`${icon} ${result.test}: ${result.status}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
      if (result.details) {
        console.log(`   Details:`, result.details);
      }
    });
  }
}

// Test Suite 3: Cross-Application Security Tests
class CrossApplicationSecurityTests {
  constructor() {
    this.results = [];
  }

  async runAllTests() {
    console.log('🔄 Starting Cross-Application Security Tests...');

    try {
      await this.testEncryptionConsistency();
      await this.testDataIsolation();
      await this.testSharedComponentSecurity();
      await this.testBackendSecurityIntegration();
      await this.testComplianceConsistency();

      this.generateReport('Cross-Application');
    } catch (error) {
      console.error('❌ Cross-Application test suite failed:', error);
      this.results.push({ test: 'Suite Execution', status: 'FAILED', error: error.message });
    }
  }

  async testEncryptionConsistency() {
    try {
      // Test that both apps use the same encryption standards
      const encryptionTests = [];

      // Check MenoWellness encryption
      window.location.href = TEST_CONFIG.MENO_WELLNESS_URL + '/profile';
      await new Promise(resolve => setTimeout(resolve, 2000));

      const menoEncryption = await SecurityTestUtils.checkEncryptionStatus();
      encryptionTests.push({
        app: 'MenoWellness',
        ...menoEncryption
      });

      // Check Partner Support encryption
      window.location.href = TEST_CONFIG.PARTNER_SUPPORT_URL + '/profile';
      await new Promise(resolve => setTimeout(resolve, 2000));

      const partnerEncryption = await SecurityTestUtils.checkEncryptionStatus();
      encryptionTests.push({
        app: 'PartnerSupport',
        ...partnerEncryption
      });

      // Compare encryption standards
      const consistentAlgorithms = encryptionTests.every(test =>
        test.algorithms && test.algorithms.includes('AES-GCM')
      );

      this.results.push({
        test: 'Encryption Consistency',
        status: consistentAlgorithms ? 'PASSED' : 'FAILED',
        details: {
          encryptionTests,
          consistentAlgorithms,
          expectedAlgorithm: 'AES-GCM'
        }
      });
    } catch (error) {
      this.results.push({
        test: 'Encryption Consistency',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async testDataIsolation() {
    try {
      // Test that apps don't leak data between each other
      const isolationTests = [];

      // Test localStorage isolation
      localStorage.setItem('test_meno_data', 'sensitive_meno_data');
      window.location.href = TEST_CONFIG.PARTNER_SUPPORT_URL;
      await new Promise(resolve => setTimeout(resolve, 1000));

      const partnerCanAccessMenoData = localStorage.getItem('test_meno_data');
      isolationTests.push({
        test: 'LocalStorage Isolation',
        isolated: !partnerCanAccessMenoData,
        details: 'Partner app should not access MenoWellness localStorage'
      });

      // Test sessionStorage isolation
      sessionStorage.setItem('test_partner_session', 'partner_session_data');
      window.location.href = TEST_CONFIG.MENO_WELLNESS_URL;
      await new Promise(resolve => setTimeout(resolve, 1000));

      const menoCanAccessPartnerSession = sessionStorage.getItem('test_partner_session');
      isolationTests.push({
        test: 'SessionStorage Isolation',
        isolated: !menoCanAccessPartnerSession,
        details: 'MenoWellness app should not access Partner sessionStorage'
      });

      // Clean up test data
      localStorage.removeItem('test_meno_data');
      sessionStorage.removeItem('test_partner_session');

      this.results.push({
        test: 'Data Isolation',
        status: isolationTests.every(t => t.isolated) ? 'PASSED' : 'FAILED',
        details: isolationTests
      });
    } catch (error) {
      this.results.push({
        test: 'Data Isolation',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async testSharedComponentSecurity() {
    try {
      // Test that shared components maintain security across apps
      const sharedComponentTests = [];

      // Test ConsentManager component
      const consentComponents = document.querySelectorAll('*:contains("consent")');
      sharedComponentTests.push({
        component: 'ConsentManager',
        found: consentComponents.length > 0,
        count: consentComponents.length
      });

      // Test SecurityAuthProvider
      const authComponents = document.querySelectorAll('*:contains("auth")') ||
                            document.querySelectorAll('[data-auth]');
      sharedComponentTests.push({
        component: 'SecurityAuthProvider',
        found: authComponents.length > 0,
        count: authComponents.length
      });

      // Test UserDataManager
      const dataComponents = document.querySelectorAll('*:contains("export")') ||
                            document.querySelectorAll('*:contains("delete")');
      sharedComponentTests.push({
        component: 'UserDataManager',
        found: dataComponents.length > 0,
        count: dataComponents.length
      });

      this.results.push({
        test: 'Shared Component Security',
        status: sharedComponentTests.filter(t => t.found).length >= 2 ? 'PASSED' : 'PARTIAL',
        details: sharedComponentTests
      });
    } catch (error) {
      this.results.push({
        test: 'Shared Component Security',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async testBackendSecurityIntegration() {
    try {
      // Test backend security function integration
      const backendTests = [];

      // Test if onboarding function is called
      const networkRequests = [];
      const originalFetch = window.fetch;

      window.fetch = async (...args) => {
        networkRequests.push({
          url: args[0],
          options: args[1],
          timestamp: Date.now()
        });
        return originalFetch.apply(window, args);
      };

      // Trigger some actions that should call backend
      const testButton = document.querySelector('button');
      if (testButton) {
        await SecurityTestUtils.simulateUserInteraction(testButton);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      // Restore fetch
      window.fetch = originalFetch;

      // Check for security-related backend calls
      const securityCalls = networkRequests.filter(req =>
        req.url.includes('onboard') ||
        req.url.includes('consent') ||
        req.url.includes('audit') ||
        req.url.includes('export') ||
        req.url.includes('delete')
      );

      backendTests.push({
        test: 'Backend Security Calls',
        totalRequests: networkRequests.length,
        securityRequests: securityCalls.length,
        securityCalls: securityCalls.map(call => ({
          url: call.url,
          hasAuth: !!(call.options?.headers?.Authorization)
        }))
      });

      this.results.push({
        test: 'Backend Security Integration',
        status: securityCalls.length > 0 ? 'PASSED' : 'PARTIAL',
        details: backendTests
      });
    } catch (error) {
      this.results.push({
        test: 'Backend Security Integration',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  async testComplianceConsistency() {
    try {
      // Test that both apps follow the same compliance standards
      const complianceTests = [];

      // Test MenoWellness compliance
      window.location.href = TEST_CONFIG.MENO_WELLNESS_URL + '/profile';
      await new Promise(resolve => setTimeout(resolve, 2000));

      const menoCompliance = {
        hasGDPR: !!(document.querySelector('*:contains("GDPR")') || document.querySelector('*:contains("European")')),
        hasHIPAA: !!(document.querySelector('*:contains("HIPAA")') || document.querySelector('*:contains("healthcare")')),
        hasPIPEDA: !!(document.querySelector('*:contains("PIPEDA")') || document.querySelector('*:contains("Canadian")')),
        hasConsent: !!document.querySelector('*:contains("consent")'),
        hasDataRights: !!(document.querySelector('*:contains("export")') && document.querySelector('*:contains("delete")'))
      };

      // Test Partner Support compliance
      window.location.href = TEST_CONFIG.PARTNER_SUPPORT_URL + '/profile';
      await new Promise(resolve => setTimeout(resolve, 2000));

      const partnerCompliance = {
        hasGDPR: !!(document.querySelector('*:contains("GDPR")') || document.querySelector('*:contains("European")')),
        hasHIPAA: !!(document.querySelector('*:contains("HIPAA")') || document.querySelector('*:contains("healthcare")')),
        hasPIPEDA: !!(document.querySelector('*:contains("PIPEDA")') || document.querySelector('*:contains("Canadian")')),
        hasConsent: !!document.querySelector('*:contains("consent")'),
        hasDataRights: !!(document.querySelector('*:contains("export")') && document.querySelector('*:contains("delete")'))
      };

      complianceTests.push({
        app: 'MenoWellness',
        ...menoCompliance
      });

      complianceTests.push({
        app: 'PartnerSupport',
        ...partnerCompliance
      });

      // Check consistency
      const consistentCompliance = Object.keys(menoCompliance).every(key =>
        menoCompliance[key] === partnerCompliance[key]
      );

      this.results.push({
        test: 'Compliance Consistency',
        status: consistentCompliance ? 'PASSED' : 'PARTIAL',
        details: {
          complianceTests,
          consistentCompliance,
          comparisonKeys: Object.keys(menoCompliance)
        }
      });
    } catch (error) {
      this.results.push({
        test: 'Compliance Consistency',
        status: 'FAILED',
        error: error.message
      });
    }
  }

  generateReport(appName) {
    const passed = this.results.filter(r => r.status === 'PASSED').length;
    const partial = this.results.filter(r => r.status === 'PARTIAL').length;
    const failed = this.results.filter(r => r.status === 'FAILED').length;
    const total = this.results.length;

    console.log(`\n📊 ${appName} Security Test Report`);
    console.log(`${'='.repeat(50)}`);
    console.log(`✅ Passed: ${passed}/${total}`);
    console.log(`⚠️  Partial: ${partial}/${total}`);
    console.log(`❌ Failed: ${failed}/${total}`);
    console.log(`📈 Success Rate: ${Math.round((passed / total) * 100)}%`);

    console.log('\n📋 Detailed Results:');
    this.results.forEach(result => {
      const icon = result.status === 'PASSED' ? '✅' :
                   result.status === 'PARTIAL' ? '⚠️' : '❌';
      console.log(`${icon} ${result.test}: ${result.status}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
      if (result.details) {
        console.log(`   Details:`, result.details);
      }
    });
  }
}

// Main Test Runner
class SecurityTestRunner {
  constructor() {
    this.allResults = [];
  }

  async runAllSecurityTests() {
    console.log('🚀 Starting Comprehensive Security and Privacy Test Suite');
    console.log('=' .repeat(70));

    const startTime = Date.now();

    try {
      // Run MenoWellness tests
      const menoTests = new MenoWellnessSecurityTests();
      await menoTests.runAllTests();
      this.allResults.push(...menoTests.results);

      // Run Partner Support tests
      const partnerTests = new PartnerSupportSecurityTests();
      await partnerTests.runAllTests();
      this.allResults.push(...partnerTests.results);

      // Run Cross-Application tests
      const crossTests = new CrossApplicationSecurityTests();
      await crossTests.runAllTests();
      this.allResults.push(...crossTests.results);

      // Generate comprehensive report
      this.generateComprehensiveReport(startTime);

    } catch (error) {
      console.error('❌ Test suite execution failed:', error);
    }
  }

  generateComprehensiveReport(startTime) {
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);

    const passed = this.allResults.filter(r => r.status === 'PASSED').length;
    const partial = this.allResults.filter(r => r.status === 'PARTIAL').length;
    const failed = this.allResults.filter(r => r.status === 'FAILED').length;
    const total = this.allResults.length;

    console.log('\n🎯 COMPREHENSIVE SECURITY TEST REPORT');
    console.log('=' .repeat(70));
    console.log(`⏱️  Total Execution Time: ${duration} seconds`);
    console.log(`📊 Total Tests: ${total}`);
    console.log(`✅ Passed: ${passed} (${Math.round((passed/total)*100)}%)`);
    console.log(`⚠️  Partial: ${partial} (${Math.round((partial/total)*100)}%)`);
    console.log(`❌ Failed: ${failed} (${Math.round((failed/total)*100)}%)`);

    const overallScore = Math.round(((passed + (partial * 0.5)) / total) * 100);
    console.log(`🏆 Overall Security Score: ${overallScore}%`);

    // Security recommendations
    console.log('\n🔒 SECURITY RECOMMENDATIONS:');
    if (failed > 0) {
      console.log('❗ HIGH PRIORITY: Address failed security tests immediately');
    }
    if (partial > 0) {
      console.log('⚠️  MEDIUM PRIORITY: Complete partially implemented security features');
    }
    if (overallScore >= 90) {
      console.log('🎉 EXCELLENT: Security implementation is comprehensive');
    } else if (overallScore >= 75) {
      console.log('👍 GOOD: Security implementation is solid with room for improvement');
    } else {
      console.log('⚠️  NEEDS IMPROVEMENT: Security implementation requires attention');
    }

    // Critical security checklist
    console.log('\n✅ CRITICAL SECURITY CHECKLIST:');
    const criticalTests = [
      'Consent Management',
      'Encryption Key Management',
      'Data Export Functionality',
      'Data Deletion Process',
      'Encryption Consistency',
      'Data Isolation'
    ];

    criticalTests.forEach(testName => {
      const result = this.allResults.find(r => r.test === testName);
      const icon = result?.status === 'PASSED' ? '✅' :
                   result?.status === 'PARTIAL' ? '⚠️' : '❌';
      console.log(`${icon} ${testName}: ${result?.status || 'NOT FOUND'}`);
    });

    console.log('\n📋 Test execution completed. Review detailed logs above for specific issues.');
  }
}

// Export for use in browser console or test environment
if (typeof window !== 'undefined') {
  window.SecurityTestRunner = SecurityTestRunner;
  window.SecurityTestUtils = SecurityTestUtils;

  // Auto-run tests if requested
  if (window.location.search.includes('autotest=true')) {
    const runner = new SecurityTestRunner();
    runner.runAllSecurityTests();
  }
}
